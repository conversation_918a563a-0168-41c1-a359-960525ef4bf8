import React from "react";
import { Switch } from "../../ui/switch";
import { Label } from "../../ui/label";
import { Button } from "../../ui/button";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "../../ui/accordionForAvailability";
import { FaTrashAlt } from "react-icons/fa";
import { FaCirclePlus } from "react-icons/fa6";
import { format } from "date-fns";
import TimezoneSelector from "../../ui/timezone-selector";

const DoctorSetAvailabilityScheduler = ({
  day,
  isActive,
  sessions,
  timezone,
  updateWeeklySessions,
  updateWeeklyTimezone,
  date,
  isForDate,
  updateOverrideSessions,
  updateOverrideTimezone,
  removeOverrideSchedule,
}) => {
  const toggleActivation = (checked) => {
    if (checked) {
      if (sessions.length === 0) {
        updateWeeklySessions(
          day,
          [{ startTime: "09:00", endTime: "10:00", slotDuration: 15 }],
          true,
        );
      } else {
        updateWeeklySessions(day, sessions, true);
      }
    } else {
      updateWeeklySessions(day, sessions, false);
    }
  };
  const handleSessionChange = (index, field, value) => {
    const updatedSessions = sessions.map((session, idx) =>
      idx === index ? { ...session, [field]: value } : session,
    );
    if (!isForDate) {
      updateWeeklySessions(day, updatedSessions);
    } else {
      updateOverrideSessions(date, updatedSessions);
    }
  };
  const handleSlotDuration = (index, duration) => {
    const updatedSessions = sessions.map((session, idx) =>
      idx === index ? { ...session, slotDuration: duration } : session,
    );
    if (!isForDate) {
      updateWeeklySessions(day, updatedSessions);
    } else {
      updateOverrideSessions(date, updatedSessions);
    }
  };
  const handleAddSession = () => {
    const newSession = {
      startTime: "09:00",
      endTime: "10:00",
      slotDuration: 15,
    };
    if (!isForDate) {
      updateWeeklySessions(day, [...sessions, newSession]);
    } else {
      updateOverrideSessions(date, [...sessions, newSession]);
    }
  };
  const handleRemoveSession = (index) => {
    const updatedSessions = sessions.filter((_, i) => i !== index);

    if (!isForDate) {
      updateWeeklySessions(day, updatedSessions);
    } else {
      updateOverrideSessions(date, updatedSessions);
    }
  };
  const generateSlots = (sessionStart, sessionEnd, slotDuration) => {
    const timeToMinutes = (timeStr) => {
      const [hours, minutes] = timeStr.split(":").map(Number);
      return hours * 60 + minutes;
    };

    const minutesToTime = (minutesTotal) => {
      let hrs = Math.floor(minutesTotal / 60);
      const mins = minutesTotal % 60;
      const suffix = hrs >= 12 ? "PM" : "AM";
      hrs = hrs % 12;
      if (hrs === 0) hrs = 12;
      return `${hrs}:${mins.toString().padStart(2, "0")} ${suffix}`;
    };

    const startMinutes = timeToMinutes(sessionStart);
    const endMinutes = timeToMinutes(sessionEnd);
    const slots = [];
    let current = startMinutes;

    while (current + slotDuration <= endMinutes) {
      const slotStart = current;
      const slotEnd = current + slotDuration;
      slots.push({
        startTime: minutesToTime(slotStart),
        endTime: minutesToTime(slotEnd),
      });
      current += slotDuration;
    }
    return slots;
  };
  const deleteOverrideSchedule = (date) => {
    removeOverrideSchedule(date);
  };

  const handleTimezoneChange = (newTimezone) => {
    if (isForDate) {
      updateOverrideTimezone(date, newTimezone);
    } else {
      updateWeeklyTimezone(day, newTimezone);
    }
  };

  return (
    <div className="flex w-full flex-col gap-4 p-4 justify-start border rounded">
      {/* Header Section */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
        <div className="flex items-center gap-2">
          {!isForDate && (
            <Switch
              checked={isActive}
              onCheckedChange={(val) => toggleActivation(val)}
            />
          )}

          <label className="font-medium">
            {isForDate
              ? format(new Date(date), "MMM dd, yyyy")
              : day.charAt(0).toUpperCase() + day.slice(1).toLowerCase()}
          </label>
        </div>

        {/* Timezone Selector */}
        {((isActive && !isForDate) || isForDate) && (
          <div className="flex-1 max-w-sm">
            <div className="mb-1">
              <Label className="text-sm text-gray-600">Timezone</Label>
            </div>
            <TimezoneSelector
              value={timezone || ""}
              onValueChange={handleTimezoneChange}
              placeholder="Select timezone..."
              className="w-full"
            />
          </div>
        )}
      </div>

      {/* Sessions Section */}
      <div className="flex flex-col w-full">
        {(!isForDate && isActive) || isForDate ? (
          <>
            {/* Timezone Warning */}
            {!timezone && (
              <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <p className="text-sm text-yellow-800">
                  ⚠️ Please select a timezone before setting your schedule
                  times.
                </p>
              </div>
            )}

            {sessions.map((session, index) => (
              <div
                key={index}
                className="flex flex-row mb-4 border p-4 rounded "
              >
                <div className="flex-1 ">
                  <div className="flex flex-col sm:flex-row items-center gap-2 mb-2">
                    <div className="flex flex-col">
                      <Label>Start Time</Label>
                      <input
                        type="time"
                        value={session.startTime}
                        onChange={(e) =>
                          handleSessionChange(
                            index,
                            "startTime",
                            e.target.value,
                          )
                        }
                        className="border rounded px-2 py-1 w-full sm:w-[150px]"
                      />
                    </div>
                    <div className="flex flex-col">
                      <Label>End Time</Label>
                      <input
                        type="time"
                        value={session.endTime}
                        onChange={(e) =>
                          handleSessionChange(index, "endTime", e.target.value)
                        }
                        className="border rounded px-2 py-1 w-full sm:w-[150px]"
                      />
                    </div>
                  </div>
                  <div className="ml-0 sm:ml-2">
                    <label className="block text-sm font-medium mb-1">
                      Choose appointment duration
                    </label>
                    <div className="flex flex-wrap gap-2">
                      {[15, 30, 45, 60].map((min) => (
                        <Button
                          key={min}
                          variant={
                            session.slotDuration === min ? "primary" : "outline"
                          }
                          onClick={() => handleSlotDuration(index, min)}
                          className="w-full sm:w-auto"
                        >
                          {min} min
                        </Button>
                      ))}
                    </div>
                  </div>
                  <Accordion type="single" collapsible className="mt-2">
                    <AccordionItem value="item-1">
                      <AccordionTrigger>
                        View Slots Detail {timezone ? `(${timezone})` : ""}
                      </AccordionTrigger>
                      <AccordionContent>
                        <div className="flex flex-wrap gap-2 p-4">
                          {timezone ? (
                            generateSlots(
                              session.startTime,
                              session.endTime,
                              session.slotDuration,
                            ).map((slot, idx) => {
                              return (
                                <div
                                  className="items-center justify-between gap-3 border border-[#000000] rounded-lg px-2 py-2"
                                  key={idx}
                                >
                                  <span className="text-sm">{`${slot.startTime}-${slot.endTime}`}</span>
                                </div>
                              );
                            })
                          ) : (
                            <p className="text-gray-500 text-sm">
                              Please select a timezone to view slot details
                            </p>
                          )}
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  </Accordion>
                </div>
                {index !== 0 && (
                  <div className=" flex items-start justify-center ">
                    <div className="">
                      <FaTrashAlt
                        className="text-[#0052FD] text-[18px] cursor-pointer"
                        onClick={() => {
                          handleRemoveSession(index);
                        }}
                      />
                    </div>
                  </div>
                )}
              </div>
            ))}
          </>
        ) : (
          <span className="text-gray-400">
            Activate the day to see sessions
          </span>
        )}
      </div>
      {((isActive && !isForDate) || isForDate) && (
        <div className="flex items-start justify-center sm:w-auto w-full gap-3">
          <FaCirclePlus
            className="text-[#0052FD] text-[35px] cursor-pointer"
            onClick={handleAddSession}
          />
          {isForDate && (
            <FaTrashAlt
              className="text-[#0052FD] text-[35px] cursor-pointer"
              onClick={() => {
                deleteOverrideSchedule(date);
              }}
            />
          )}
        </div>
      )}
    </div>
  );
};

export default DoctorSetAvailabilityScheduler;
