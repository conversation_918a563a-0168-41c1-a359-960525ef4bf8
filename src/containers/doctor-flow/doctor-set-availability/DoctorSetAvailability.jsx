import React, { useEffect, useState } from "react";
import { <PERSON><PERSON> } from "../../../components/ui/button";
import { Calendar } from "../../../components/ui/calendar";
import AddManual from "../../../assets/images/illustration.png";
import {
  <PERSON><PERSON>,
  <PERSON>et<PERSON>ontent,
  SheetHeader,
  Sheet<PERSON>itle,
} from "../../../components/ui/sheet";
import { ReactComponent as CalenderIcon } from "../../../assets/svgs/CalendarIcon.svg";
import { format } from "date-fns";
import DoctorSetAvailibilitySheduler from "../../../components/doctor-side-components/doctor-set-availability-sheduler/DoctorSetAvailabilityScheduler";
import { toast } from "../../../hooks/use-toast";
import {
  createOverrideScheduleOfDoctor_api,
  createWeeklyScheduleOfDoctor_api,
  getOverrideScheduleOfDoctor_api,
  getWeeklyScheduleOfDoctor_api,
} from "../../../api/api_calls/schedule_apiCalls";
import { FaPlus } from "react-icons/fa";
import { useNavigate } from "react-router-dom";
import { parse } from "date-fns";
import LoadingSpinner from "../../../components/animations/LoadingSpinner";

const SetAvailability = () => {
  const [isSheetOpen, setIsSheetOpen] = useState(false);
  const [weeklySessions, setWeeklySessions] = useState([]);
  const [overrideSessions, setOverrideSessions] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    fetchInitialData();
  }, []);

  const fetchInitialData = async () => {
    setIsLoading(true);
    try {
      await Promise.all([fetchWeeklySchedule(), fetchOverrideSchedule()]);
    } finally {
      setIsLoading(false);
    }
  };
  const fetchWeeklySchedule = async () => {
    try {
      const res = await getWeeklyScheduleOfDoctor_api();
      const daysOfWeek = [
        "monday",
        "tuesday",
        "wednesday",
        "thursday",
        "friday",
        "saturday",
        "sunday",
      ];
      const transformedMap = {};

      res.data.forEach((dayObj) => {
        const { dayOfWeek, sessions, timezone } = dayObj;
        const transformedSessions = sessions.map((session) => ({
          startTime:
            session.startTime.length > 5
              ? session.startTime.slice(0, 5)
              : session.startTime,
          endTime:
            session.endTime.length > 5
              ? session.endTime.slice(0, 5)
              : session.endTime,
          slotDuration: session.slotDuration,
        }));
        transformedMap[dayOfWeek.toLowerCase()] = {
          dayOfWeek: dayOfWeek.toLowerCase(),
          isActive: true,
          sessions: transformedSessions,
          timezone: timezone || "", // Include timezone from backend
        };
      });

      const finalArray = daysOfWeek.map((day) => {
        if (transformedMap[day]?.sessions?.length > 0) {
          return transformedMap[day];
        }
        return {
          dayOfWeek: day,
          isActive: false,
          sessions: [],
          timezone: "", // Default empty timezone for new days
        };
      });
      setWeeklySessions(finalArray);
    } catch (error) {
      toast({
        description: error.message,
        variant: "destructive",
      });
      console.log("ERROR IN fetchWeeklySchedule  => ", error);
    }
  };
  const fetchOverrideSchedule = async () => {
    try {
      const res = await getOverrideScheduleOfDoctor_api();
      const finalArray = [];
      res.data.forEach((schedule) => {
        const { date, sessions, timezone } = schedule;
        const transformedSessions = sessions.map((session) => {
          return {
            startTime:
              session.startTime.length > 5
                ? session.startTime.slice(0, 5)
                : session.startTime,
            endTime:
              session.endTime.length > 5
                ? session.endTime.slice(0, 5)
                : session.endTime,
            slotDuration: session.slotDuration,
          };
        });
        finalArray.push({
          date: date,
          sessions: transformedSessions,
          timezone: timezone || "", // Include timezone from backend
        });
      });
      setOverrideSessions(finalArray);
    } catch (error) {
      toast({
        description: error.message,
        variant: "destructive",
      });
      console.log("ERROR IN fetchWeeklySchedule  => ", error);
    }
  };
  const submitWeeklySchedule = async () => {
    // Validate that active days have timezones
    const activeDaysWithoutTimezone = weeklySessions.filter(
      (session) => session.isActive && !session.timezone,
    );

    if (activeDaysWithoutTimezone.length > 0) {
      toast({
        description: `Please select timezone for: ${activeDaysWithoutTimezone.map((d) => d.dayOfWeek).join(", ")}`,
        variant: "destructive",
      });
      return;
    }

    setIsSaving(true);
    try {
      const weeklySchedule = weeklySessions.map((session) => {
        if (session.isActive) {
          return {
            dayOfWeek: session.dayOfWeek,
            sessions: session.sessions,
            timezone: session.timezone,
          };
        } else {
          return {
            dayOfWeek: session.dayOfWeek,
            sessions: [],
            timezone: session.timezone || null,
          };
        }
      });
      const res = await createWeeklyScheduleOfDoctor_api({
        schedules: weeklySchedule,
      });
      toast({
        description: res.message,
      });
    } catch (error) {
      toast({
        description: error.message,
        variant: "destructive",
      });
      console.log("ERROR IN submitWeeklySchedule  => ", error);
    } finally {
      setIsSaving(false);
    }
  };
  const submitOverrideSchedule = async () => {
    // Validate that all override schedules have timezones
    const schedulesWithoutTimezone = overrideSessions.filter(
      (session) => !session.timezone,
    );

    if (schedulesWithoutTimezone.length > 0) {
      toast({
        description: `Please select timezone for all override dates`,
        variant: "destructive",
      });
      return;
    }

    setIsSaving(true);
    try {
      const res = await createOverrideScheduleOfDoctor_api({
        overrideSchedules: overrideSessions,
      });
      toast({
        description: res.message,
      });
    } catch (error) {
      toast({
        description: error.message,
        variant: "destructive",
      });
      console.log("ERROR IN submitOverrideSchedule  => ", error);
    } finally {
      setIsSaving(false);
    }
  };
  const updateWeeklySessions = (dayOfWeek, newSessions, newActive) => {
    setWeeklySessions((prev) =>
      prev.map((day) =>
        day.dayOfWeek === dayOfWeek
          ? {
              ...day,
              sessions: newSessions,
              isActive: newActive !== undefined ? newActive : day.isActive,
            }
          : day,
      ),
    );
  };

  const updateWeeklyTimezone = (dayOfWeek, timezone) => {
    setWeeklySessions((prev) =>
      prev.map((day) =>
        day.dayOfWeek === dayOfWeek
          ? {
              ...day,
              timezone: timezone,
            }
          : day,
      ),
    );
  };
  const updateOverrideSessions = (date, newSessions) => {
    setOverrideSessions((prev) =>
      prev.map((session) =>
        session.date === date
          ? {
              ...session,
              sessions: newSessions,
            }
          : session,
      ),
    );
  };

  const updateOverrideTimezone = (date, timezone) => {
    setOverrideSessions((prev) =>
      prev.map((session) =>
        session.date === date
          ? {
              ...session,
              timezone: timezone,
            }
          : session,
      ),
    );
  };
  const removeOverrideSchedule = (date) => {
    const newSessions = overrideSessions.filter(
      (session) => session.date !== date,
    );
    setOverrideSessions(newSessions);
  };
  const addOverrideSchedule = (date) => {
    setOverrideSessions((prev) => {
      return [
        ...prev,
        {
          date: date,
          sessions: [
            { startTime: "09:00", endTime: "10:00", slotDuration: 15 },
          ],
          timezone: "", // Default empty timezone for new override schedules
        },
      ];
    });
  };

  return (
    <div className="flex items-start justify-start flex-col w-full">
      {isLoading && (
        <div className="absolute inset-0 bg-white bg-opacity-80 flex items-center justify-center z-50">
          <LoadingSpinner />
        </div>
      )}

      {isSaving && (
        <div className="absolute inset-0 bg-white bg-opacity-80 flex items-center justify-center z-50">
          <div className="flex flex-col items-center">
            <LoadingSpinner />
            <p className="mt-4 text-primary font-medium">Saving changes...</p>
          </div>
        </div>
      )}

      <h2 className="text-2xl font-bold mb-4 w-full">Set your Availability</h2>

      <div className="w-full mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
        <div className="mb-3">
          <h3 className="text-lg font-semibold text-blue-900 mb-1">
            Timezone-Aware Scheduling
          </h3>
          <p className="text-sm text-blue-700">
            Set timezone for each day individually. This allows you maximum
            flexibility for your availability.
          </p>
        </div>
      </div>

      <div className="flex items-end justify-end w-full gap-4">
        {overrideSessions.length !== 0 && (
          <Button variant={"primary"} onClick={() => setIsSheetOpen(true)}>
            <FaPlus />
            Add Manually
          </Button>
        )}

        <Button
          variant={"primary"}
          onClick={() => navigate("/doctor/view-calendar")}
        >
          View Calendar
        </Button>
      </div>

      <div className="flex items-start justify-center gap-4 flex-row w-full">
        <div className="w-6/12 shadow-xl rounded-xl">
          <div className="relative">
            {weeklySessions.map((dayItem) => (
              <DoctorSetAvailibilitySheduler
                key={dayItem.dayOfWeek}
                day={dayItem.dayOfWeek}
                isActive={dayItem.isActive}
                sessions={dayItem.sessions}
                timezone={dayItem.timezone}
                updateWeeklySessions={updateWeeklySessions}
                updateWeeklyTimezone={updateWeeklyTimezone}
              />
            ))}
          </div>

          <div className="mt-5 flex justify-end items-center p-5 gap-5">
            <Button
              variant="secondary"
              onClick={() => {
                fetchWeeklySchedule();
              }}
              disabled={isLoading || isSaving}
            >
              Cancel
            </Button>
            <Button
              variant="primary"
              onClick={() => {
                submitWeeklySchedule();
              }}
              disabled={isLoading || isSaving}
            >
              Save
            </Button>
          </div>
        </div>

        <div className="w-6/12 rounded-xl shadow-xl p-4 min-h-[400px] relative">
          {overrideSessions.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-full">
              <img
                alt="Add manually illustration"
                src={AddManual}
                className="w-[209px] h-[209px] mx-auto mb-4"
              />
              <Button variant="primary" onClick={() => setIsSheetOpen(true)}>
                + Add Manually
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {overrideSessions.map((overrideItem) => (
                <DoctorSetAvailibilitySheduler
                  key={overrideItem.date}
                  isForDate={true}
                  date={overrideItem.date}
                  sessions={overrideItem.sessions}
                  timezone={overrideItem.timezone}
                  updateOverrideSessions={updateOverrideSessions}
                  updateOverrideTimezone={updateOverrideTimezone}
                  removeOverrideSchedule={removeOverrideSchedule}
                />
              ))}
            </div>
          )}
          <div className="flex justify-end gap-2 pt-4">
            <Button
              variant="secondary"
              onClick={() => {
                fetchOverrideSchedule();
              }}
              disabled={isLoading || isSaving}
            >
              Cancel
            </Button>
            <Button
              variant="primary"
              onClick={() => {
                submitOverrideSchedule();
              }}
              disabled={isLoading || isSaving}
            >
              Save
            </Button>
          </div>
          <Sheet open={isSheetOpen} onOpenChange={setIsSheetOpen}>
            <SheetContent className="w-[700px] sm:max-w-[700px] max-h-screen flex flex-col overflow-y-auto">
              <SheetHeader>
                <SheetTitle>Set your Availability</SheetTitle>
              </SheetHeader>
              <DateSelectionForm
                addOverrideSchedule={addOverrideSchedule}
                onCancel={() => setIsSheetOpen(false)}
                initialDates={overrideSessions?.map((session) => {
                  return parse(session.date, "yyyy-MM-dd", new Date());
                })}
              />
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </div>
  );
};

const DateSelectionForm = ({ addOverrideSchedule, onCancel, initialDates }) => {
  const [dates, setDates] = useState(
    initialDates?.length > 0 ? [...initialDates] : [],
  );

  const handleNext = () => {
    const initialDateSet = new Set(
      initialDates.map((d) => format(d, "yyyy-MM-dd")),
    );

    dates.forEach((date) => {
      const dateString = format(date, "yyyy-MM-dd");
      if (!initialDateSet.has(dateString)) {
        addOverrideSchedule(dateString);
      }
    });

    onCancel();
  };

  return (
    <div className="flex flex-col h-full">
      <div className="w-full items-center justify-between flex-row flex">
        <span className="text-[16px] font-bold">
          Select Dates{" "}
          <span className="opacity-50 font-normal">
            (you can select up to 5 days at once)
          </span>
        </span>
        <CalenderIcon />
      </div>

      <div className="flex-1 flex flex-col justify-between">
        <Calendar
          mode="multiple"
          selected={dates}
          onSelect={(selected) => {
            setDates([...selected]);
          }}
          className="w-full"
          numberOfMonths={1}
          disabled={[{ before: new Date() }, ...initialDates]}
        />
        <div className="flex justify-end gap-2 pt-4">
          <Button variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button
            variant="primary"
            onClick={handleNext}
            disabled={!dates.length}
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  );
};

export default SetAvailability;
